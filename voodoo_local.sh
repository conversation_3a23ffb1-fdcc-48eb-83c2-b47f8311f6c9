#!/bin/bash

# Function to kill processes on specific ports
kill_processes_on_ports() {
    local ports=("$@")
    echo "Killing processes on ports: ${ports[*]}"

    for port in "${ports[@]}"; do
        echo "Checking port $port..."
        # Find processes using the port
        pids=$(lsof -ti :$port 2>/dev/null)
        if [ -n "$pids" ]; then
            echo "Killing processes on port $port: $pids"
            echo "$pids" | xargs kill -9 2>/dev/null || true
            sleep 1
            # Double check if processes are still running
            remaining_pids=$(lsof -ti :$port 2>/dev/null)
            if [ -n "$remaining_pids" ]; then
                echo "Force killing remaining processes on port $port: $remaining_pids"
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
            fi
        else
            echo "No processes found on port $port"
        fi
    done
    echo "Port cleanup completed"
}

# Define ports used by the specific gRPC API services we're starting
GRPC_SERVICE_PORTS=(
    # Main gRPC service ports - only the ones we're starting
    8085    # entity-post-grpc-api
    8086    # entity-moment-grpc-api
    8082    # entity-user-grpc-api
    8080    # streaks-streaks-grpc-api

    # Technical/health check ports for these specific services
    3001    # entity-moment-grpc-api, entity-user-grpc-api, streaks-streaks-grpc-api
    3006    # entity-post-grpc-api

    # TLS ports for these services (if enabled)
    8081    # TLS port for gRPC services
)

echo "Starting Voodoo Local Development Environment"
echo "============================================="

# Kill processes on gRPC service ports only
kill_processes_on_ports "${GRPC_SERVICE_PORTS[@]}"

echo ""
echo "Starting gRPC services in iTerm..."

osascript <<EOF
tell application "iTerm"
    activate
    set newWindow to (create window with default profile)

    -- Tab 1: start-entity-post-grpc-api
    tell newWindow
        set tab1 to (create tab with default profile)
        tell current session of tab1
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "echo 'Starting entity-post-grpc-api on port 8085...'"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-post-grpc-api"
        end tell
    end tell

    -- Tab 2: start-entity-moment-grpc-api
    tell newWindow
        set tab2 to (create tab with default profile)
        tell current session of tab2
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "echo 'Starting entity-moment-grpc-api on port 8086...'"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-moment-grpc-api"
        end tell
    end tell

    -- Tab 3: start-entity-user-grpc-api
    tell newWindow
        set tab3 to (create tab with default profile)
        tell current session of tab3
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "echo 'Starting entity-user-grpc-api on port 8082...'"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-user-grpc-api"
        end tell
    end tell

    -- Tab 4: start-streaks-streaks-grpc-api (with delay to allow other services to start)
    tell newWindow
        set tab4 to (create tab with default profile)
        tell current session of tab4
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "echo 'Waiting 10 seconds for other services to initialize...'"
            write text "sleep 10"
            write text "echo 'Starting streaks-streaks-grpc-api on port 8080...'"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-streaks-streaks-grpc-api"
        end tell
    end tell
end tell
EOF

echo ""
echo "============================================="
echo "Voodoo Local Development Environment Started"
echo "============================================="
echo ""
echo "Services started in iTerm tabs:"
echo "  • entity-post-grpc-api    (gRPC: 8085, Technical: 3006)"
echo "  • entity-moment-grpc-api  (gRPC: 8086, Technical: 3001)"
echo "  • entity-user-grpc-api    (gRPC: 8082, Technical: 3001)"
echo "  • streaks-streaks-grpc-api (gRPC: 8080, Technical: 3001)"
echo ""
echo "Note: Dependencies (PubSub Emulator, Redis, Postgres) are expected to be running separately."
echo "Only the gRPC API service ports were cleaned up before starting."
echo ""
echo "Check iTerm for service startup logs and any errors."
echo "Services may take a few moments to fully initialize."
