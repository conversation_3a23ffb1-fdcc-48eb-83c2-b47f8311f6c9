# includes:file://./../../../../config/templates/local/logging.yaml
# includes:file://./../../../../config/templates/local/datadog.yaml
# includes:file://./../../../../config/templates/local/service-account.yaml
# includes:file://./../../../../config/templates/local/grpc-api.yaml
# includes:file://./../../../../config/templates/local/auth.yaml
# includes:file://./../../../../config/templates/local/profiler.yaml
# includes:file://./../../../../config/templates/local/entity-user-spanner.yaml
# includes:file://./../../../../config/templates/local/safety-queue-publisher-sample-rates.yaml
# includes:file://./../../../../config/templates/local/microservices.yaml

application:
  technicalPort: 3001
  namespace: streaks
  domain: streaks
  feature: streaks
  name: streaks-streaks-grpc-api
  type: api
  env: local
  version: local

build-info:
  app: streaks-streaks-grpc-api
  version: main
  branch: main
  commit: main

grpcApi:
  <<: *grpcApi
  sendDetailedErrors: true
logging: *logging
serviceAccount: *serviceAccount
datadog: *datadog

spanner: 
  <<: *entityUsersSpanner
  minOpened: 8_000
  maxOpened: 10_000
  maxIdle: 9_000
  gRPCConnectionPoolSize: 400

momentClient:
  <<: *entityMoment
  lastNMoments: 50
  disableMomentRefresh: true

postClient: *entityPost
userClient: *entityUser  
