# Code generated by ./domains/tooling/genconfig-go; DO NOT EDIT.
backend-go-apps:
  configFiles:
    config.yml: |
      # Code generated by ./domains/tooling/genconfig-go; DO NOT EDIT.
      
      #region included-conf
      # file: ./../../../../config/templates/local/logging.yaml
      tmpl-logging: &logging
        level: debug
        simple: true
      
      # file: ./../../../../config/templates/local/datadog.yaml
      tmpl-datadog: &datadog
        enable: false
        debug: false
        sampling: 1
      
      # file: ./../../../../config/templates/local/service-account.yaml
      tmpl-serviceAccount: &serviceAccount
        projectID: "backend-core-dev"
      
      tmpl-serviceAccountWithCredentials: &serviceAccountWithCredentials
        <<: *serviceAccount
        credentials:
          type: "service_account"
          clientEmail: "<EMAIL>"
          privateKey: sm://backend-core-sa-fasterstore
      # file: ./../../../../config/templates/local/grpc-api.yaml
      tmpl-grpcApi: &grpcApi
        port: 8080
        grpcReflection: true
        host: localhost
        sendDetailedErrors: true
        maxConnectionAge: "30s"
        maxConnectionAgeGrace: "10s"
        signatureSecret: "" # Disabling it if not provided
        gracefulStopDuration: 1s
        TLSPort: 8081
        enableTLS: false
        withStats: true
      
      # file: ./../../../../config/templates/local/auth.yaml
      tmpl-auth: &auth
        jwtSecret: BEREAL_INTEGRATION
        # jwtSecret: sm://backend-core-auth-jwt-key
      
      # file: ./../../../../config/templates/local/profiler.yaml
      tmpl-profiler: &profiler
        port: 6060
        host: localhost
      # file: ./../../../../config/templates/local/entity-user-spanner.yaml
      tmpl-entityUsersSpanner: &entityUsersSpanner
        database: "projects/backend-core-dev/instances/bereal-local-instance/databases/entity-user"
        minOpened: 100
        maxOpened: 10_000
        maxIdle: 1_000
        trackSessionHandles: false
        
      # file: ./../../../../config/templates/local/safety-queue-publisher-sample-rates.yaml
      tmpl-safety-queue-publisher-sample-rates: &safetyQueuePublisherSampleRates
        - key: conversation_message_created
          value: 0.0
        - key: post_created
          value: 0.0
        - key: profile_photo_created
          value: 0.0
        - key: best_post
          value: 1.0
      
      # file: ./../../../../config/templates/local/microservices.yaml
      toolingGatekeeperGrpcPort: &toolingGatekeeperGrpcPort 9999
      tmpl-toolingGatekeeper: &toolingGatekeeper
        addr: "localhost:9999"
      toolingExperimentGrpcPort: &toolingExperimentGrpcPort 9998
      tmpl-toolingExperiment: &toolingExperiment
        addr: "localhost:9998"
      toolingPublisherGrpcPort: &toolingPublisherGrpcPort 8080
      tmpl-toolingPublisher: &toolingPublisher
        addr: "localhost:8080"
      entityUserGrpcPort: &entityUserGrpcPort 8082
      tmpl-entityUser: &entityUser
        addr: "localhost:8082"
      tmpl-entityTopic: &entityTopic
        addr: "localhost:8101"
      relationshipGraphGrpcPort: &relationshipGraphGrpcPort 8083
      tmpl-relationshipGraph: &relationshipGraph
        addr: "localhost:8083"
      officialAccountsBackendGrpcPort: &officialAccountsBackendGrpcPort 8084
      tmpl-officialAccountsBackend: &officialAccountsBackend
        addr: "localhost:8084"
      entityPostGrpcPort: &entityPostGrpcPort 8085
      tmpl-entityPost: &entityPost
        addr: "localhost:8085"
      entityMomentGrpcPort: &entityMomentGrpcPort 8086
      tmpl-entityMoment: &entityMoment
        addr: "localhost:8086"
      relationshipTagGrpcPort: &relationshipTagGrpcPort 8087
      tmpl-relationshipTag: &relationshipTag
        addr: "localhost:8087"
      entityUserCacheGrpcPort: &entityUserCacheGrpcPort 8088
      tmpl-entityUserCache: &entityUserCache
        addr: "localhost:8088"
      entityEventGrpcPort: &entityEventGrpcPort 8089
      tmpl-entityEvent: &entityEvent
        addr: "localhost:8089"
      relationshipFriendRequestGrpcPort: &relationshipFriendRequestGrpcPort 8090
      tmpl-relationshipFriendRequest: &relationshipFriendRequest
        addr: "localhost:8090"
      searchOABackendGrpcPort: &searchOABackendGrpcPort 8092
      tmpl-searchOABackend: &searchOABackend
        addr: "localhost:8092"
      searchBackendGrpcPort: &searchBackendGrpcPort 8093
      tmpl-searchBackend: &searchBackend
        addr: "localhost:8093"
      eventBackendGrpcPort: &eventBackendGrpcPort 8094
      tmpl-eventBackend: &eventBackend
        addr: "localhost:8094"
      notificationOverlordGrpcPort: &notificationOverlordGrpcPort 8095
      tmpl-notificationOverlord: &notificationOverlord
        addr: "127.0.0.1:8095"
      notificationMinionGrpcPort: &notificationMinionGrpcPort 8096
      tmpl-notificationMinion: &notificationMinion
        addr: "localhost:8096"
      entityCounterGrpcPort: &entityCounterGrpcPort 8097
      tmpl-entityCounter: &entityCounter
        addr: "localhost:8097"
      relationshipRankingGrpcPort: &relationshipRankingGrpcPort 8098
      tmpl-relationshipRanking: &relationshipRanking
        addr: "localhost:8098"
      entityActivityGrpcPort: &entityActivityGrpcPort 8099
      tmpl-entityActivity: &entityActivity
        addr: "localhost:8099"
      entityExploreGrpcPort: &entityExploreGrpcPort 8100
      tmpl-entityExplore: &entityExplore
        addr: "localhost:8100"
      tmpl-geoip: &geoip
        addr: "localhost:8101"
      tmpl-usersAtRisk: &usersAtRisk
        addr: "localhost:8102"
      tmpl-feedMemories: &feedMemories
        addr: "localhost:8103"
      tmpl-relationshipBackend: &relationshipBackend
        addr: "localhost:8104"
      
      #endregion
      application:
        technicalPort: 3001
        namespace: streaks
        domain: streaks
        feature: streaks
        name: streaks-streaks-grpc-api
        type: api
        env: local
        version: local
      
      build-info:
        app: streaks-streaks-grpc-api
        version: main
        branch: main
        commit: main
      
      grpcApi:
        <<: *grpcApi
        sendDetailedErrors: true
      logging: *logging
      serviceAccount: *serviceAccount
      datadog: *datadog
      
      spanner: 
        <<: *entityUsersSpanner
        minOpened: 8_000
        maxOpened: 10_000
        maxIdle: 9_000
        gRPCConnectionPoolSize: 400
      
      momentClient:
        <<: *entityMoment
        lastNMoments: 50
        disableMomentRefresh: true
      
      postClient: *entityPost
      userClient: *entityUser
