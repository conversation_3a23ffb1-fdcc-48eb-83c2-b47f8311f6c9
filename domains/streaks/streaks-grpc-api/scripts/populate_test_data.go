package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"cloud.google.com/go/civil"
	gS "cloud.google.com/go/spanner"
)

// TestData contains all the test data we'll insert
type TestData struct {
	UserID        string
	Region        string
	InitialStreak uint64
	Posts         []TestPost
	Moments       []TestMoment
}

type TestPost struct {
	ID        string
	UserID    string
	MomentID  string
	CreatedAt time.Time
}

type TestMoment struct {
	ID       string
	FiredAt  time.Time
	Timezone string
}

func main() {
	ctx := context.Background()

	// Initialize multiple Spanner clients for different databases
	databases := map[string]*gS.Client{
		"entity-user": nil,
		"entity-post": nil,
	}

	// Connect to all required databases
	for dbName := range databases {
		databasePath := fmt.Sprintf("projects/backend-core-dev/instances/bereal-local-instance/databases/%s", dbName)
		client, err := gS.NewClient(ctx, databasePath)
		if err != nil {
			log.Fatalf("Failed to create Spanner client for %s: %v", dbName, err)
		}
		databases[dbName] = client
		defer client.Close()
	}

	fmt.Println("Connected to local Spanner emulator (multiple databases)")

	// Define test scenarios
	scenarios := []TestData{
		{
			UserID:        "user-single-gap",
			Region:        "us-central",
			InitialStreak: 2,
		},
		{
			UserID:        "user-multiple-gaps",
			Region:        "us-central",
			InitialStreak: 1,
		},
		{
			UserID:        "user-perfect-streak",
			Region:        "us-central",
			InitialStreak: 7,
		},
		{
			UserID:        "user-too-many-gaps",
			Region:        "us-central",
			InitialStreak: 1,
		},
	}

	// Clean up existing data first
	fmt.Println("Cleaning up existing test data...")
	err := cleanupTestData(ctx, databases, scenarios)
	if err != nil {
		log.Printf("Warning: Failed to cleanup existing data: %v", err)
	}

	// Populate test data for each scenario
	for _, scenario := range scenarios {
		fmt.Printf("Setting up test data for user: %s\n", scenario.UserID)

		err := setupScenario(ctx, databases, scenario)
		if err != nil {
			log.Fatalf("Failed to setup scenario for %s: %v", scenario.UserID, err)
		}
	}

	fmt.Println("\n✅ Test data population complete!")
	fmt.Println("\nTest Users Created:")
	fmt.Println("==================")
	fmt.Println("user-single-gap      - Has posts on day 0 and day 2 (missing day 1)")
	fmt.Println("user-multiple-gaps   - Has posts on days 0, 3, 6 (missing days 1, 2, 4, 5)")
	fmt.Println("user-perfect-streak  - Has posts every day for 7 days (no gaps)")
	fmt.Println("user-too-many-gaps   - Has very few posts over 20 days (many gaps)")
	fmt.Println("\nNow you can run the gRPC client scripts to test the endpoints!")
}

func setupScenario(ctx context.Context, databases map[string]*gS.Client, data TestData) error {
	now := time.Now()

	// Setup moments and posts based on scenario
	switch data.UserID {
	case "user-single-gap":
		data.Moments = createMoments(now, 7)
		data.Posts = createPosts(data.UserID, data.Moments, []int{0, 2}) // Missing day 1

	case "user-multiple-gaps":
		data.Moments = createMoments(now, 7)
		data.Posts = createPosts(data.UserID, data.Moments, []int{0, 3, 6}) // Missing days 1, 2, 4, 5

	case "user-perfect-streak":
		data.Moments = createMoments(now, 7)
		data.Posts = createPosts(data.UserID, data.Moments, []int{0, 1, 2, 3, 4, 5, 6}) // All days

	case "user-too-many-gaps":
		data.Moments = createMoments(now, 20)                                    // 20 days of moments
		data.Posts = createPosts(data.UserID, data.Moments, []int{0, 5, 10, 15}) // Only a few posts
	}

	// Insert user (in entity-user database)
	err := insertUser(ctx, databases["entity-user"], data.UserID, data.Region)
	if err != nil {
		return fmt.Errorf("failed to insert user: %w", err)
	}

	// Insert posts (in entity-post database)
	err = insertPosts(ctx, databases["entity-post"], data.Posts)
	if err != nil {
		return fmt.Errorf("failed to insert posts: %w", err)
	}

	// Set initial streak (in entity-user database)
	err = setUserStreak(ctx, databases["entity-user"], data.UserID, data.InitialStreak, data.Moments[0].ID)
	if err != nil {
		return fmt.Errorf("failed to set user streak: %w", err)
	}

	fmt.Printf("  ✅ User %s setup complete (streak: %d, posts: %d, moments: %d)\n",
		data.UserID, data.InitialStreak, len(data.Posts), len(data.Moments))

	return nil
}

func createMoments(baseTime time.Time, days int) []TestMoment {
	moments := make([]TestMoment, days)
	for i := 0; i < days; i++ {
		momentTime := baseTime.AddDate(0, 0, -i)
		moments[i] = TestMoment{
			ID:       fmt.Sprintf("moment-%d", i),
			FiredAt:  momentTime,
			Timezone: "America/Los_Angeles",
		}
	}
	return moments
}

func createPosts(userID string, moments []TestMoment, dayIndices []int) []TestPost {
	var posts []TestPost
	for _, dayIndex := range dayIndices {
		if dayIndex < len(moments) {
			moment := moments[dayIndex]
			posts = append(posts, TestPost{
				ID:        fmt.Sprintf("%s-post-%d", userID, dayIndex),
				UserID:    userID,
				MomentID:  moment.ID,
				CreatedAt: moment.FiredAt,
			})
		}
	}
	return posts
}

func insertUser(ctx context.Context, client *gS.Client, userID, region string) error {
	now := time.Now()
	mutation := gS.InsertOrUpdate(
		"Users",
		[]string{"UserId", "Username", "PhoneNumber", "Region", "CreatedAt", "UpdatedAt", "CreatedBy", "UpdatedBy"},
		[]interface{}{userID, userID + "-username", "+1234567890", region, now, now, "test-system", "test-system"},
	)

	_, err := client.Apply(ctx, []*gS.Mutation{mutation})
	return err
}

func insertPosts(ctx context.Context, client *gS.Client, posts []TestPost) error {
	if len(posts) == 0 {
		return nil
	}

	var mutations []*gS.Mutation
	for _, post := range posts {
		now := time.Now()
		mutation := gS.InsertOrUpdate(
			"posts",
			[]string{"PostID", "UserID", "PostType", "PostFormat", "MomentID", "IsMain", "CreatedAt", "UpdatedAt"},
			[]interface{}{post.ID, post.UserID, 1, 1, post.MomentID, true, post.CreatedAt, now},
		)
		mutations = append(mutations, mutation)
	}

	_, err := client.Apply(ctx, mutations)
	return err
}

func setUserStreak(ctx context.Context, client *gS.Client, userID string, length uint64, lastMomentID string) error {
	lastPostDay := civil.DateOf(time.Now())
	now := time.Now()

	mutation := gS.InsertOrUpdate(
		"Streaks",
		[]string{"UserId", "Length", "LastPostCalendarDay", "UpdatedAt", "UpdatedBy"},
		[]interface{}{userID, int64(length), lastPostDay, now, "test-system"},
	)

	_, err := client.Apply(ctx, []*gS.Mutation{mutation})
	return err
}

func cleanupTestData(ctx context.Context, databases map[string]*gS.Client, scenarios []TestData) error {
	// Clean up data for all test users
	// StreakCompensation, Streaks, Users are in entity-user database
	// posts is in entity-post database

	userTables := []string{"StreakCompensation", "Streaks", "Users"}

	for _, scenario := range scenarios {
		// Clean up user-related tables
		for _, table := range userTables {
			if table == "StreakCompensation" {
				// StreakCompensation has composite key (UserId, Date), so we need to query first
				// For now, skip cleanup of StreakCompensation as it may not exist yet
				continue
			}
			mutation := gS.Delete(table, gS.Key{scenario.UserID})
			_, err := databases["entity-user"].Apply(ctx, []*gS.Mutation{mutation})
			if err != nil {
				log.Printf("Warning: Failed to delete from %s for user %s: %v", table, scenario.UserID, err)
			}
		}

		// Note: Skipping cleanup of posts table (will be overwritten with new data)
	}

	return nil
}
