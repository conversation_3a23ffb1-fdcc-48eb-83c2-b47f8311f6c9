#!/bin/bash

# Script to run the complete streak recovery test suite
# This script assumes you have the local development stack running

set -e

echo "🚀 Starting Streak Recovery Test Suite"
echo "====================================="

# Check if Go is available
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed or not in PATH"
    exit 1
fi

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
echo "📁 Script directory: $SCRIPT_DIR"

# Check if the local development stack is running
echo ""
echo "🔍 Checking prerequisites..."

# Check if Spanner emulator is running (you might need to adjust this check)
if ! pgrep -f "spanner" > /dev/null; then
    echo "⚠️  Warning: Spanner emulator might not be running"
    echo "   Please ensure you've run 'make setup-stack' to start the local development stack"
fi

# Step 1: Populate test data
echo ""
echo "1️⃣  Populating test data in local Spanner..."
echo "=============================================="
cd "$SCRIPT_DIR"
go run populate_test_data.go

if [ $? -ne 0 ]; then
    echo "❌ Failed to populate test data"
    exit 1
fi

echo ""
echo "✅ Test data populated successfully"

# Wait a moment for data to settle
sleep 2

# Step 2: Start the streaks service (you'll need to do this manually or update this script)
echo ""
echo "2️⃣  Starting streaks service..."
echo "==============================="
echo "ℹ️  You need to start the streaks service manually:"
echo "   cd /path/to/streaks-grpc-api"
echo "   make start-streaks-streaks-grpc-api"
echo ""
echo "Press Enter when the streaks service is running on localhost:50051..."
read -p ""

# Step 3: Test Calculate endpoint
echo ""
echo "3️⃣  Testing CalculateStreakRecovery endpoint..."
echo "=============================================="
go run test_calculate_recovery.go

if [ $? -ne 0 ]; then
    echo "❌ CalculateStreakRecovery tests failed"
    exit 1
fi

echo ""
echo "✅ CalculateStreakRecovery tests completed"

# Wait a moment
sleep 2

# Step 4: Test Apply endpoint
echo ""
echo "4️⃣  Testing ApplyStreakRecovery endpoint..."
echo "=========================================="
go run test_apply_recovery.go

if [ $? -ne 0 ]; then
    echo "❌ ApplyStreakRecovery tests failed"
    exit 1
fi

echo ""
echo "✅ ApplyStreakRecovery tests completed"

# Step 5: Summary
echo ""
echo "🎉 ALL TESTS COMPLETED SUCCESSFULLY!"
echo "==================================="
echo ""
echo "📊 Test Summary:"
echo "- ✅ Test data populated"
echo "- ✅ CalculateStreakRecovery endpoint tested"
echo "- ✅ ApplyStreakRecovery endpoint tested"
echo "- ✅ Validation scenarios tested"
echo ""
echo "🔍 Next steps for verification:"
echo "1. Check local Spanner database for StreakCompensation records"
echo "2. Verify user streak lengths have been updated"
echo "3. Review the console output above for any warnings"
echo ""
echo "💡 To clean up test data, you can run the populate script again"
echo "   (it cleans up before inserting new data)"