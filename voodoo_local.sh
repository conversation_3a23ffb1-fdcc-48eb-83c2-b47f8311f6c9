#!/bin/bash

osascript <<EOF
tell application "iTerm"
    activate
    set newWindow to (create window with default profile)

    -- Tab 1: start-entity-post-grpc-api (with 10s delay)
    tell newWindow
        set tab1 to (create tab with default profile)
        tell current session of tab1
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-post-grpc-api"
        end tell
    end tell

    -- Tab 2: start-entity-moment-grpc-api
    tell newWindow
        set tab2 to (create tab with default profile)
        tell current session of tab2
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-moment-grpc-api"
        end tell
    end tell

    -- Tab 3: start-entity-user-grpc-api
    tell newWindow
        set tab3 to (create tab with default profile)
        tell current session of tab3
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-user-grpc-api"
        end tell
    end tell

    -- Tab 4: start-streaks-streaks-grpc-api
    tell newWindow
        set tab4 to (create tab with default profile)
        tell current session of tab4
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "sleep 20 && PUBSUB_EMULATOR_HOST=localhost:8686 make start-streaks-streaks-grpc-api"
        end tell
    end tell
end tell
EOF
