#!/bin/bash

# Function to kill processes on specific ports
kill_processes_on_ports() {
    local ports=("$@")
    echo "Killing processes on ports: ${ports[*]}"

    for port in "${ports[@]}"; do
        echo "Checking port $port..."
        # Find processes using the port
        pids=$(lsof -ti :$port 2>/dev/null)
        if [ -n "$pids" ]; then
            echo "Killing processes on port $port: $pids"
            echo "$pids" | xargs kill -9 2>/dev/null || true
            sleep 1
            # Double check if processes are still running
            remaining_pids=$(lsof -ti :$port 2>/dev/null)
            if [ -n "$remaining_pids" ]; then
                echo "Force killing remaining processes on port $port: $remaining_pids"
                echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
            fi
        else
            echo "No processes found on port $port"
        fi
    done
    echo "Port cleanup completed"
}

# Define ports used by the gRPC services and dependencies
GRPC_PORTS=(
    # Main gRPC service ports
    8085    # entity-post-grpc-api
    8086    # entity-moment-grpc-api
    8082    # entity-user-grpc-api
    8080    # streaks-streaks-grpc-api (default grpc port)

    # Technical/health check ports
    3001    # entity-moment-grpc-api, entity-user-grpc-api, streaks-streaks-grpc-api
    3006    # entity-post-grpc-api

    # TLS ports
    8081    # TLS port for gRPC services

    # Dependencies that might conflict
    8686    # PubSub Emulator
    6380    # Redis
    5432    # Postgres

    # Other microservice ports that might be running
    9999    # tooling-gatekeeper-grpc-api
    9998    # tooling-experiment-grpc-api
    8083    # relationship-graph-grpc-api
    8084    # official-accounts-backend-grpc-api
    8087    # relationship-tag-grpc-api
    8088    # entity-user-cache-grpc-api
    8089    # entity-event-grpc-api
    8090    # relationship-friend-request-grpc-api
    8092    # search-oa-backend-grpc-api
    8093    # search-backend-grpc-api
    8094    # event-backend-grpc-api
    8095    # notification-overlord-grpc-api
    8096    # notification-minion-grpc-api
    8097    # entity-counter-grpc-api
    8098    # relationship-ranking-grpc-api
    8099    # entity-activity-grpc-api
    8100    # entity-explore-grpc-api
    8101    # entity-topic-grpc-api / geoip
    8102    # users-at-risk
    8103    # feed-memories
    8104    # relationship-backend
)

echo "Starting Voodoo Local Development Environment"
echo "============================================="

# Kill processes on all relevant ports
kill_processes_on_ports "${GRPC_PORTS[@]}"

echo ""
echo "Starting gRPC services in iTerm..."

osascript <<EOF
tell application "iTerm"
    activate
    set newWindow to (create window with default profile)

    -- Tab 1: start-entity-post-grpc-api
    tell newWindow
        set tab1 to (create tab with default profile)
        tell current session of tab1
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "echo 'Starting entity-post-grpc-api on port 8085...'"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-post-grpc-api"
        end tell
    end tell

    -- Tab 2: start-entity-moment-grpc-api
    tell newWindow
        set tab2 to (create tab with default profile)
        tell current session of tab2
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "echo 'Starting entity-moment-grpc-api on port 8086...'"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-moment-grpc-api"
        end tell
    end tell

    -- Tab 3: start-entity-user-grpc-api
    tell newWindow
        set tab3 to (create tab with default profile)
        tell current session of tab3
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "echo 'Starting entity-user-grpc-api on port 8082...'"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-entity-user-grpc-api"
        end tell
    end tell

    -- Tab 4: start-streaks-streaks-grpc-api (with delay to allow other services to start)
    tell newWindow
        set tab4 to (create tab with default profile)
        tell current session of tab4
            write text "cd /Users/<USER>/Desktop/Freelance/Voodoo/backend-go"
            write text "echo 'Waiting 10 seconds for other services to initialize...'"
            write text "sleep 10"
            write text "echo 'Starting streaks-streaks-grpc-api on port 8080...'"
            write text "PUBSUB_EMULATOR_HOST=localhost:8686 make start-streaks-streaks-grpc-api"
        end tell
    end tell
end tell
EOF
