package internal

import (
	"context"
	"fmt"
	"sort"
	"time"

	"cloud.google.com/go/civil"
	gS "cloud.google.com/go/spanner"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	pbCommon "github.com/BeReal-App/backend-go/proto/common/core/v1"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	pbPost "github.com/BeReal-App/backend-go/proto/private/post/v2"
	"github.com/BeReal-App/backend-go/shared/model"
	"github.com/BeReal-App/backend-go/shared/scope"
)

// CalculateStreakRecovery calculates what the user's streak would be if missing days were filled
func (s *Server) CalculateStreakRecovery(ctx context.Context, req *pbUser.CalculateStreakRecoveryRequest) (*pbUser.CalculateStreakRecoveryResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "CalculateStreakRecovery")

	l.Info().
		Str("user_id", req.UserId).
		Int64("number_of_days", int64(req.NumberOfDays)).
		Msg("Starting streak recovery calculation")

	// Determine the number of days to look back, defaulting to 30 if not set or invalid
	numberOfDays := int(req.NumberOfDays)
	if numberOfDays <= 0 {
		l.Debug().
			Str("user_id", req.UserId).
			Int64("requested_days", int64(req.NumberOfDays)).
			Int("default_days", 30).
			Msg("Invalid number of days provided, using default")
		numberOfDays = 30
	}

	// Get user's basic info to determine region
	l.Debug().Str("user_id", req.UserId).Msg("Fetching user basic info")
	userBasicInfo, err := s.getUserBasicInfo(ctx, req.UserId)
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get user basic info")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: fmt.Sprintf("Failed to get user info: %v", err),
			},
		}, nil
	}
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userBasicInfo.Region).
		Msg("Retrieved user basic info")

	// Get current streak
	l.Debug().Str("user_id", req.UserId).Msg("Fetching current streak")

	// Add nil check for userServiceClient
	if s.userServiceClient == nil {
		l.Error().
			Str("user_id", req.UserId).
			Msg("userServiceClient is nil")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: "User service client not initialized",
			},
		}, nil
	}

	currentStreak, err := s.userServiceClient.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: req.UserId})
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get current streak")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: fmt.Sprintf("Failed to get current streak: %v", err),
			},
		}, nil
	}

	// Add nil check for currentStreak response
	if currentStreak == nil || currentStreak.Streak == nil {
		l.Error().
			Str("user_id", req.UserId).
			Bool("current_streak_nil", currentStreak == nil).
			Bool("streak_nil", currentStreak != nil && currentStreak.Streak == nil).
			Msg("Received nil streak response")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: "Received invalid streak response",
			},
		}, nil
	}

	l.Debug().
		Str("user_id", req.UserId).
		Uint64("current_streak", currentStreak.Streak.Length).
		Msg("Retrieved current streak")

	// Parse user's region
	userRegion := model.NewRegion(userBasicInfo.Region)
	if userRegion == model.RegionUnknown {
		l.Error().
			Str("user_id", req.UserId).
			Str("region", userBasicInfo.Region).
			Msg("Invalid user region")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: "Invalid user region",
			},
		}, nil
	}
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userRegion.String()).
		Msg("Parsed user region")

	// Get all moments for the user's region
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userRegion.String()).
		Msg("Fetching moments for user region")

	// Add nil check for momentsClient
	if s.momentsClient == nil {
		l.Error().
			Str("user_id", req.UserId).
			Msg("momentsClient is nil")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: "Moments client not initialized",
			},
		}, nil
	}

	allMomentsByRegion := s.momentsClient.GetAllByRegion()
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userRegion.String()).
		Int("total_regions", len(allMomentsByRegion)).
		Msg("Retrieved moments by region")

	userRegionMoments, exists := allMomentsByRegion[userRegion]

	// Add nil check for userRegionMoments
	var momentsCount int
	if userRegionMoments != nil {
		momentsCount = userRegionMoments.Len()
	} else {
		momentsCount = 0
	}

	if !exists || userRegionMoments == nil || momentsCount == 0 {
		l.Warn().
			Str("user_id", req.UserId).
			Str("region", userRegion.String()).
			Bool("exists", exists).
			Bool("moments_nil", userRegionMoments == nil).
			Int("moments_count", momentsCount).
			Msg("No moments found for user region, creating test moments for local development")

		// Create test moments for local development
		testMoments := s.createTestMoments(userRegion, numberOfDays)
		if len(testMoments) == 0 {
			return &pbUser.CalculateStreakRecoveryResponse{
				Calculation: &pbUser.StreakRecoveryCalculation{
					IsEligible:   false,
					ErrorMessage: "No moments found for user region and failed to create test moments",
				},
			}, nil
		}

		l.Info().
			Str("user_id", req.UserId).
			Str("region", userRegion.String()).
			Int("test_moments_created", len(testMoments)).
			Msg("Created test moments for local development")

		// Use test moments for the calculation
		sortedMoments := testMoments

		// Continue with the rest of the logic using test moments
		// Get all user's posts (no time limit for estimated streak calculation)
		l.Debug().Str("user_id", req.UserId).Msg("Fetching all user posts")
		userPosts, err := s.getAllUserPosts(ctx, req.UserId)
		if err != nil {
			l.Warn().
				Str("user_id", req.UserId).
				Err(err).
				Msg("Failed to get user posts, continuing with empty slice")
			userPosts = []UserPost{}
		}
		l.Debug().
			Str("user_id", req.UserId).
			Int("posts_count", len(userPosts)).
			Msg("Retrieved user posts")

		// Create a map of moment IDs to check if user posted
		postsByMomentID := make(map[string]bool)
		for _, post := range userPosts {
			if post.MomentID.Valid {
				postsByMomentID[post.MomentID.StringVal] = true
				l.Debug().
					Str("user_id", req.UserId).
					Str("post_id", post.PostID).
					Str("moment_id", post.MomentID.StringVal).
					Time("created_at", post.CreatedAt).
					Msg("Found user post with moment ID")
			} else {
				l.Debug().
					Str("user_id", req.UserId).
					Str("post_id", post.PostID).
					Time("created_at", post.CreatedAt).
					Msg("Found user post without moment ID")
			}
		}
		l.Info().
			Str("user_id", req.UserId).
			Int("total_posts", len(userPosts)).
			Int("posts_with_moments", len(postsByMomentID)).
			Msg("Created map of posts by moment ID")

		// Find gaps in the last N days (recovery is only allowed for recent gaps)
		now := time.Now()
		lookbackAgo := now.AddDate(0, 0, -numberOfDays)
		l.Debug().
			Str("user_id", req.UserId).
			Time("now", now).
			Time("lookback_threshold", lookbackAgo).
			Int("lookback_days", numberOfDays).
			Msg("Calculating lookback period for gap detection")

		var gapsToFill []*pbUser.StreakGap
		// Track which dates have already been added
		dateAdded := make(map[string]bool)

		for _, moment := range sortedMoments {
			// Only consider moments from the last N days for gap filling
			if moment.FiredAt.Before(lookbackAgo) {
				continue
			}

			// Generate a date key in YYYY-MM-DD format
			dateKey := moment.FiredAt.Format("2006-01-02")

			// Skip if we've already added a gap for this date
			if dateAdded[dateKey] {
				continue
			}

			// Check if user posted for this moment
			hasPost := postsByMomentID[moment.ID]
			l.Debug().
				Str("user_id", req.UserId).
				Str("moment_id", moment.ID).
				Str("date", dateKey).
				Bool("has_post", hasPost).
				Msg("Checking moment for gap")

			if !hasPost {
				gapsToFill = append(gapsToFill, &pbUser.StreakGap{
					Date: &pbCommon.Date{
						Year:  int32(moment.FiredAt.Year()),
						Month: int32(moment.FiredAt.Month()),
						Day:   int32(moment.FiredAt.Day()),
					},
					MomentId: moment.ID,
				})
				dateAdded[dateKey] = true

				l.Debug().
					Str("user_id", req.UserId).
					Str("moment_id", moment.ID).
					Str("date", dateKey).
					Msg("Found gap to fill")
			} else {
				l.Debug().
					Str("user_id", req.UserId).
					Str("moment_id", moment.ID).
					Str("date", dateKey).
					Msg("User has post for this moment - no gap")
			}
		}

		l.Info().
			Str("user_id", req.UserId).
			Int("gaps_found", len(gapsToFill)).
			Msg("Identified gaps to fill for streak recovery")

		// Calculate estimated streak after filling gaps (uses all posts to find true break point)
		l.Debug().
			Str("user_id", req.UserId).
			Int("gaps_to_fill", len(gapsToFill)).
			Msg("Calculating estimated streak")
		estimatedStreak := s.calculateEstimatedStreak(ctx, req.UserId, sortedMoments, postsByMomentID, gapsToFill)

		isEligible := len(gapsToFill) > 0 && len(gapsToFill) <= 10

		l.Info().
			Str("user_id", req.UserId).
			Uint64("current_streak", currentStreak.Streak.Length).
			Uint64("estimated_streak", estimatedStreak).
			Int("gaps_count", len(gapsToFill)).
			Bool("is_eligible", isEligible).
			Msg("Completed streak recovery calculation using test moments")

		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				CurrentStreak:   currentStreak.Streak.Length,
				EstimatedStreak: estimatedStreak,
				GapsToFill:      gapsToFill,
				IsEligible:      isEligible,
			},
		}, nil
	}
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userRegion.String()).
		Int("moments_count", userRegionMoments.Len()).
		Msg("Retrieved moments for user region")

	// Sort moments by FiredAt desc (most recent first)
	userRegionMomentsList := userRegionMoments.AsMutableSlice()
	l.Debug().
		Str("user_id", req.UserId).
		Int("total_moments", len(userRegionMomentsList)).
		Msg("Processing moments for sorting")

	sortedMoments := make([]models.Moment, 0, userRegionMoments.Len())
	var unfiredCount int
	for i, moment := range userRegionMomentsList {
		l.Debug().
			Str("user_id", req.UserId).
			Int("moment_index", i).
			Str("moment_id", moment.ID).
			Bool("fired_at_nil", moment.FiredAt == nil).
			Msg("Processing moment")

		// Only include fired moments
		if moment.FiredAt != nil {
			sortedMoments = append(sortedMoments, moment)
		} else {
			unfiredCount++
		}
	}
	l.Debug().
		Str("user_id", req.UserId).
		Int("fired_moments", len(sortedMoments)).
		Int("unfired_moments", unfiredCount).
		Msg("Filtered out unfired moments")

	// Add defensive checks before sorting
	sort.Slice(sortedMoments, func(i, j int) bool {
		if i >= len(sortedMoments) || j >= len(sortedMoments) {
			l.Error().
				Str("user_id", req.UserId).
				Int("i", i).
				Int("j", j).
				Int("slice_len", len(sortedMoments)).
				Msg("Index out of bounds in sort function")
			return false
		}
		if sortedMoments[i].FiredAt == nil || sortedMoments[j].FiredAt == nil {
			l.Error().
				Str("user_id", req.UserId).
				Int("i", i).
				Int("j", j).
				Bool("i_fired_at_nil", sortedMoments[i].FiredAt == nil).
				Bool("j_fired_at_nil", sortedMoments[j].FiredAt == nil).
				Msg("Nil FiredAt in sort function")
			return false
		}
		return sortedMoments[i].FiredAt.After(*sortedMoments[j].FiredAt)
	})
	l.Debug().
		Str("user_id", req.UserId).
		Int("sorted_moments", len(sortedMoments)).
		Msg("Sorted moments by FiredAt (descending)")

	// Get all user's posts (no time limit for estimated streak calculation)
	l.Debug().Str("user_id", req.UserId).Msg("Fetching all user posts")
	userPosts, err := s.getAllUserPosts(ctx, req.UserId)
	if err != nil {
		l.Warn().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get user posts, continuing with empty slice")
		userPosts = []UserPost{}
	}
	l.Debug().
		Str("user_id", req.UserId).
		Int("posts_count", len(userPosts)).
		Msg("Retrieved user posts")

	// Create a map of moment IDs to check if user posted
	postsByMomentID := make(map[string]bool)
	for _, post := range userPosts {
		if post.MomentID.Valid {
			postsByMomentID[post.MomentID.StringVal] = true
		}
	}
	l.Debug().
		Str("user_id", req.UserId).
		Int("unique_moment_posts", len(postsByMomentID)).
		Msg("Created map of posts by moment ID")

	// Find gaps in the last N days (recovery is only allowed for recent gaps)
	now := time.Now()
	lookbackAgo := now.AddDate(0, 0, -numberOfDays)
	l.Debug().
		Str("user_id", req.UserId).
		Time("now", now).
		Time("lookback_threshold", lookbackAgo).
		Int("lookback_days", numberOfDays).
		Msg("Calculating lookback period for gap detection")

	var gapsToFill []*pbUser.StreakGap
	// Track which dates have already been added
	dateAdded := make(map[string]bool)

	for _, moment := range sortedMoments {
		// Only consider moments from the last N days for gap filling
		if moment.FiredAt.Before(lookbackAgo) {
			continue
		}

		// Generate a date key in YYYY-MM-DD format
		dateKey := moment.FiredAt.Format("2006-01-02")

		// Skip if we've already added a gap for this date
		if dateAdded[dateKey] {
			continue
		}

		// Check if user posted for this moment
		if !postsByMomentID[moment.ID] {
			gapsToFill = append(gapsToFill, &pbUser.StreakGap{
				Date: &pbCommon.Date{
					Year:  int32(moment.FiredAt.Year()),
					Month: int32(moment.FiredAt.Month()),
					Day:   int32(moment.FiredAt.Day()),
				},
				MomentId: moment.ID,
			})
			dateAdded[dateKey] = true

			l.Debug().
				Str("user_id", req.UserId).
				Str("moment_id", moment.ID).
				Str("date", dateKey).
				Msg("Found gap to fill")
		}
	}

	l.Info().
		Str("user_id", req.UserId).
		Int("gaps_found", len(gapsToFill)).
		Msg("Identified gaps to fill for streak recovery")

	// Calculate estimated streak after filling gaps (uses all posts to find true break point)
	l.Debug().
		Str("user_id", req.UserId).
		Int("gaps_to_fill", len(gapsToFill)).
		Msg("Calculating estimated streak")
	estimatedStreak := s.calculateEstimatedStreak(ctx, req.UserId, sortedMoments, postsByMomentID, gapsToFill)

	isEligible := len(gapsToFill) > 0 && len(gapsToFill) <= 10

	l.Info().
		Str("user_id", req.UserId).
		Uint64("current_streak", currentStreak.Streak.Length).
		Uint64("estimated_streak", estimatedStreak).
		Int("gaps_count", len(gapsToFill)).
		Bool("is_eligible", isEligible).
		Msg("Completed streak recovery calculation")

	return &pbUser.CalculateStreakRecoveryResponse{
		Calculation: &pbUser.StreakRecoveryCalculation{
			CurrentStreak:   currentStreak.Streak.Length,
			EstimatedStreak: estimatedStreak,
			GapsToFill:      gapsToFill,
			IsEligible:      isEligible,
		},
	}, nil
}

// ApplyStreakRecovery applies streak recovery by inserting compensation records and recomputing the streak
func (s *Server) ApplyStreakRecovery(ctx context.Context, req *pbUser.ApplyStreakRecoveryRequest) (*pbUser.ApplyStreakRecoveryResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "ApplyStreakRecovery")

	l.Info().
		Str("user_id", req.UserId).
		Int("gaps_count", len(req.GapsToFill)).
		Msg("Starting streak recovery application")

	// Validate input
	if req.UserId == "" {
		l.Error().Msg("Invalid user ID: empty")
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: empty")
	}

	if len(req.GapsToFill) == 0 {
		l.Error().
			Str("user_id", req.UserId).
			Msg("No gaps provided for recovery")
		return nil, status.Errorf(codes.InvalidArgument, "no gaps provided for recovery")
	}

	if len(req.GapsToFill) > 10 {
		l.Error().
			Str("user_id", req.UserId).
			Int("gaps_count", len(req.GapsToFill)).
			Msg("Too many gaps for recovery (max 10)")
		return nil, status.Errorf(codes.InvalidArgument, "too many gaps for recovery: %d (max 10)", len(req.GapsToFill))
	}

	// Log gap details
	for i, gap := range req.GapsToFill {
		l.Debug().
			Str("user_id", req.UserId).
			Int("gap_index", i).
			Str("moment_id", gap.MomentId).
			Int32("year", gap.Date.Year).
			Int32("month", gap.Date.Month).
			Int32("day", gap.Date.Day).
			Msg("Gap details for filling")
	}

	// Get user's current streak before applying recovery
	currentStreak, err := s.userServiceClient.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: req.UserId})
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get current streak before applying recovery")
	} else {
		l.Info().
			Str("user_id", req.UserId).
			Uint64("current_streak", currentStreak.Streak.Length).
			Msg("Current streak before applying recovery")
	}

	// Get user's basic info to verify they exist
	l.Debug().
		Str("user_id", req.UserId).
		Msg("Fetching user basic info")
	userBasicInfo, err := s.getUserBasicInfo(ctx, req.UserId)
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get user info")
		return nil, fmt.Errorf("failed to get user info for userID %s: %w", req.UserId, err)
	}
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userBasicInfo.Region).
		Msg("Retrieved user basic info")

	// Insert StreakCompensation records
	l.Debug().
		Str("user_id", req.UserId).
		Int("gaps_count", len(req.GapsToFill)).
		Msg("Inserting streak compensation records")
	err = s.insertStreakCompensationRecords(ctx, req.UserId, req.GapsToFill)
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to insert streak compensation records")
		return nil, fmt.Errorf("failed to insert streak compensation records: %w", err)
	}
	l.Info().
		Str("user_id", req.UserId).
		Int("gaps_filled", len(req.GapsToFill)).
		Msg("Successfully inserted streak compensation records")

	// Get the updated streak after compensation
	l.Debug().
		Str("user_id", req.UserId).
		Msg("Fetching updated streak after compensation")
	newStreakResponse, err := s.userServiceClient.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: req.UserId})
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get updated streak after compensation")
		return nil, fmt.Errorf("failed to get updated streak after compensation: %w", err)
	}

	// Calculate streak difference
	var streakDifference uint64
	if currentStreak != nil {
		streakDifference = newStreakResponse.Streak.Length - currentStreak.Streak.Length
	}

	l.Info().
		Str("user_id", req.UserId).
		Uint64("old_streak_length", func() uint64 {
			if currentStreak != nil {
				return currentStreak.Streak.Length
			}
			return 0
		}()).
		Uint64("new_streak_length", newStreakResponse.Streak.Length).
		Uint64("streak_increase", streakDifference).
		Str("region", userBasicInfo.Region).
		Msg("Completed streak recovery successfully")

	return &pbUser.ApplyStreakRecoveryResponse{
		NewStreak: newStreakResponse.Streak,
	}, nil
}

// insertStreakCompensationRecords inserts multiple StreakCompensation records in a single transaction
func (s *Server) insertStreakCompensationRecords(ctx context.Context, userID string, gaps []*pbUser.StreakGap) error {
	l := scope.GetLoggerForCallsite(ctx, "insertStreakCompensationRecords")

	l.Info().
		Str("user_id", userID).
		Int("gaps_count", len(gaps)).
		Msg("Starting to insert streak compensation records")

	// Prepare mutations for batch insert
	var mutations []*gS.Mutation
	for _, gap := range gaps {
		// Convert pbCommon.Date to civil.Date for the Date column
		spannerDate := civil.Date{
			Year:  int(gap.Date.Year),
			Month: time.Month(gap.Date.Month),
			Day:   int(gap.Date.Day),
		}

		mutation := gS.InsertOrUpdate(
			"StreakCompensation",
			[]string{"UserId", "Date", "MomentID"},
			[]interface{}{userID, spannerDate, gap.MomentId},
		)
		mutations = append(mutations, mutation)

		l.Debug().
			Str("user_id", userID).
			Str("date", spannerDate.String()).
			Str("moment_id", gap.MomentId).
			Msg("Prepared StreakCompensation mutation")
	}

	l.Debug().
		Str("user_id", userID).
		Int("mutations_count", len(mutations)).
		Msg("Executing batch insert transaction")

	// Execute batch insert in a transaction
	_, err := s.spannerClient.ReadWriteTransactionWithOptions(
		ctx,
		func(_ context.Context, tx *gS.ReadWriteTransaction) error {
			return tx.BufferWrite(mutations)
		},
		gS.TransactionOptions{
			TransactionTag: "ApplyStreakRecovery",
		},
	)
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to insert StreakCompensation records")
		return fmt.Errorf("failed to insert StreakCompensation records: %w", err)
	}

	l.Info().
		Str("user_id", userID).
		Int("records_inserted", len(gaps)).
		Msg("Successfully inserted compensation records")

	return nil
}

// UserPost represents a simplified post structure for streak calculations
type UserPost struct {
	PostID    string
	UserID    string
	MomentID  gS.NullString
	CreatedAt time.Time
}

// getAllUserPosts retrieves all user posts (no time limit) via post service
func (s *Server) getAllUserPosts(ctx context.Context, userID string) ([]UserPost, error) {
	l := scope.GetLoggerForCallsite(ctx, "getAllUserPosts")

	// Call the post service to get user posts
	// IMPORTANT: SearchAll=true forces the service to query the main 'posts' table
	// instead of the 'user_posts' table which only contains recent posts
	l.Debug().
		Str("user_id", userID).
		Msg("Calling post service GetPostsOfUser")

	resp, err := s.postClient.GetPostsOfUser(ctx, &pbPost.GetPostsOfUserRequest{
		UserId: userID,
		Order: &pbPost.OrderBy{
			Field:     pbPost.OrderBy_FIELD_CREATED_AT,
			Direction: pbPost.OrderBy_DIRECTION_DESC,
		},
		IncludeDeleted: func() *bool { b := false; return &b }(), // Exclude deleted posts for streak calculation
		SearchAll:      func() *bool { b := true; return &b }(),  // Search full table for all posts
		// No limit - we need all posts for accurate streak calculation
	})
	if err != nil {
		l.Error().
			Err(err).
			Str("user_id", userID).
			Msg("Failed to call post service GetPostsOfUser")
		return nil, fmt.Errorf("failed to get user posts from post service: %w", err)
	}

	l.Debug().
		Str("user_id", userID).
		Int("posts_returned", len(resp.Posts)).
		Int("posts_with_interactions", len(resp.PostsWithInteractions)).
		Msg("Post service response received")

	// Convert proto posts to UserPost structs
	var posts []UserPost
	for _, protoPost := range resp.Posts {
		post := UserPost{
			PostID:    protoPost.Id,
			UserID:    protoPost.UserId,
			CreatedAt: protoPost.CreatedAt.AsTime(),
		}

		// Set MomentID if available
		if protoPost.MomentId != "" {
			post.MomentID = gS.NullString{StringVal: protoPost.MomentId, Valid: true}
		} else {
			post.MomentID = gS.NullString{Valid: false}
		}

		posts = append(posts, post)
	}

	return posts, nil
}

// calculateEstimatedStreak calculates what the streak would be if gaps were filled
// This function uses all user posts (not time-limited) to determine where the streak would break
func (s *Server) calculateEstimatedStreak(_ context.Context, _ string, sortedMoments []models.Moment, postsByMomentID map[string]bool, gapsToFill []*pbUser.StreakGap) uint64 {
	// Create a map of gaps to fill for easier lookup
	gapMomentIDs := make(map[string]bool)
	for _, gap := range gapsToFill {
		gapMomentIDs[gap.MomentId] = true
	}

	// Calculate streak from most recent moment backwards
	// Since we have all user posts, we can accurately determine where the streak would break
	consecutiveDays := 0

	for _, moment := range sortedMoments {
		// Check if user posted or if this is a gap we plan to fill
		hasPost := postsByMomentID[moment.ID] || gapMomentIDs[moment.ID]

		if hasPost {
			consecutiveDays++
		} else {
			// Break in the streak - stop counting
			break
		}
	}

	return uint64(consecutiveDays)
}

// getUserBasicInfo retrieves user basic info via user service
func (s *Server) getUserBasicInfo(ctx context.Context, userID string) (*pbUser.UserBasicInfo, error) {
	l := scope.GetLoggerForCallsite(ctx, "getUserBasicInfo")

	// Get user basic info from user service
	userBasicInfoResp, err := s.userServiceClient.GetUserBasicInfo(ctx, &pbUser.GetUserBasicInfoRequest{
		UserId: userID,
	})
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to get user basic info from user service")
		return nil, err
	}

	l.Debug().
		Str("user_id", userID).
		Str("region", userBasicInfoResp.BasicInfo.Region).
		Msg("Retrieved user basic info from user service")

	return userBasicInfoResp.BasicInfo, nil
}

// createTestMoments creates mock moments for local development/testing
func (s *Server) createTestMoments(region model.Region, numberOfDays int) []models.Moment {
	now := time.Now()
	testMoments := make([]models.Moment, 0, numberOfDays)

	// Create moments for the last numberOfDays days
	// Use the same ID format as the test data: "moment-0", "moment-1", etc.
	for i := 0; i < numberOfDays; i++ {
		momentTime := now.AddDate(0, 0, -i)
		// Set to a reasonable time like 2 PM
		momentTime = time.Date(momentTime.Year(), momentTime.Month(), momentTime.Day(), 14, 0, 0, 0, momentTime.Location())

		moment := models.Moment{
			ID:        fmt.Sprintf("moment-%d", i), // Match the test data format
			Region:    region,
			FiredAt:   &momentTime,
			LocalDate: momentTime.Format("2006-01-02"),
		}
		testMoments = append(testMoments, moment)
	}

	// Sort by FiredAt desc (most recent first)
	sort.Slice(testMoments, func(i, j int) bool {
		return testMoments[i].FiredAt.After(*testMoments[j].FiredAt)
	})

	return testMoments
}
